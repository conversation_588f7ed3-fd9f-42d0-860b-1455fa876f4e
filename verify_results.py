#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
验证协变量数据处理结果
"""

import pandas as pd
import numpy as np

def main():
    """
    验证处理结果
    """
    print("=" * 60)
    print("协变量数据处理结果验证")
    print("=" * 60)
    
    # 读取原始数据和处理后数据
    original_file = "194个国家1960-2023存在缺失值的751个协变量.csv"
    processed_file = "筛选后协变量特征值列表.csv"
    
    try:
        # 读取原始数据
        print("读取原始数据...")
        df_original = pd.read_csv(original_file, encoding='utf-8')
        print(f"原始数据形状: {df_original.shape}")
        
        # 读取处理后数据
        print("读取处理后数据...")
        df_processed = pd.read_csv(processed_file, encoding='utf-8')
        print(f"处理后数据形状: {df_processed.shape}")
        
        print("\n" + "=" * 40)
        print("数据对比分析")
        print("=" * 40)
        
        # 基本统计
        print(f"原始数据:")
        print(f"  - 行数: {df_original.shape[0]:,}")
        print(f"  - 列数: {df_original.shape[1]:,}")
        print(f"  - 缺失值数量: {df_original.isnull().sum().sum():,}")
        print(f"  - 缺失值比例: {df_original.isnull().sum().sum() / (df_original.shape[0] * df_original.shape[1]) * 100:.2f}%")
        
        print(f"\n处理后数据:")
        print(f"  - 行数: {df_processed.shape[0]:,}")
        print(f"  - 列数: {df_processed.shape[1]:,}")
        print(f"  - 缺失值数量: {df_processed.isnull().sum().sum():,}")
        print(f"  - 缺失值比例: {df_processed.isnull().sum().sum() / (df_processed.shape[0] * df_processed.shape[1]) * 100:.2f}%")
        
        # 变量筛选效果
        variables_removed = df_original.shape[1] - df_processed.shape[1]
        retention_rate = df_processed.shape[1] / df_original.shape[1] * 100
        
        print(f"\n变量筛选效果:")
        print(f"  - 剔除变量数量: {variables_removed:,}")
        print(f"  - 保留变量数量: {df_processed.shape[1]:,}")
        print(f"  - 变量保留率: {retention_rate:.2f}%")
        
        # 数据完整性检查
        print(f"\n数据完整性检查:")
        if df_processed.isnull().sum().sum() == 0:
            print("  [OK] 所有缺失值已成功填充")
        else:
            print("  [ERROR] 仍存在缺失值")

        # 数据范围检查（检查前几个数值列）
        print(f"\n数据范围检查（前5个数值列）:")
        numeric_cols = df_processed.select_dtypes(include=[np.number]).columns[:5]
        for col in numeric_cols:
            if col in df_processed.columns:
                min_val = df_processed[col].min()
                max_val = df_processed[col].max()
                mean_val = df_processed[col].mean()
                print(f"  - {col}: 范围[{min_val:.4f}, {max_val:.4f}], 均值={mean_val:.4f}")

        # 检查是否有异常值（无穷大或NaN）
        print(f"\n异常值检查:")
        has_inf = np.isinf(df_processed.select_dtypes(include=[np.number])).any().any()
        has_nan = df_processed.isnull().any().any()

        if not has_inf and not has_nan:
            print("  [OK] 数据中无异常值（无穷大或NaN）")
        else:
            if has_inf:
                print("  [ERROR] 数据中存在无穷大值")
            if has_nan:
                print("  [ERROR] 数据中存在NaN值")

        print("\n" + "=" * 60)
        print("验证完成！")
        print("=" * 60)

        # 总结
        print(f"\n处理总结:")
        print(f"[OK] 成功从 {df_original.shape[1]:,} 个变量筛选出 {df_processed.shape[1]:,} 个变量")
        print(f"[OK] 成功填充了 {df_original.isnull().sum().sum():,} 个缺失值")
        print(f"[OK] 数据形状保持为 {df_processed.shape[0]:,} 行")
        print(f"[OK] 输出文件: {processed_file}")
        
    except Exception as e:
        print(f"验证过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
