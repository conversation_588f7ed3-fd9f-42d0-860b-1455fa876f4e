#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
协变量数据处理脚本
严格按照utilts.py中的数据处理逻辑进行操作
"""

import pandas as pd
import numpy as np
from utilts import filter_variables, min_max_knn_impute
import os

def main():
    """
    主函数：处理协变量数据
    """
    # 输入文件路径
    input_file = "194个国家1960-2023存在缺失值的751个协变量.csv"
    output_file = "筛选后协变量特征值列表.csv"
    
    print("=" * 60)
    print("协变量数据处理开始")
    print("=" * 60)
    
    # 检查输入文件是否存在
    if not os.path.exists(input_file):
        print(f"错误：输入文件 '{input_file}' 不存在！")
        return
    
    try:
        # 1. 读取数据
        print(f"正在读取数据文件: {input_file}")
        df = pd.read_csv(input_file, encoding='utf-8')
        print(f"原始数据形状: {df.shape}")
        print(f"原始数据列数: {len(df.columns)}")
        print(f"原始数据行数: {len(df)}")
        
        # 显示数据基本信息
        print("\n数据基本信息:")
        print(f"缺失值总数: {df.isnull().sum().sum()}")
        print(f"缺失值比例: {df.isnull().sum().sum() / (df.shape[0] * df.shape[1]) * 100:.2f}%")
        
        # 2. 使用utilts.py中的filter_variables函数进行变量筛选
        print("\n" + "=" * 40)
        print("步骤1: 变量筛选")
        print("=" * 40)
        
        # 按照utilts.py中的默认参数设置
        missing_threshold = 0.7  # 缺失值比例阈值50%
        variance_threshold = 0.01  # 方差阈值0.01
        
        print(f"缺失值比例阈值: {missing_threshold}")
        print(f"方差阈值: {variance_threshold}")
        
        df_filtered = filter_variables(
            df, 
            missing_threshold=missing_threshold, 
            variance_threshold=variance_threshold
        )
        
        print(f"筛选后数据形状: {df_filtered.shape}")
        print(f"剔除的变量数量: {len(df.columns) - len(df_filtered.columns)}")
        
        # 3. 使用utilts.py中的min_max_knn_impute函数进行KNN插值
        print("\n" + "=" * 40)
        print("步骤2: KNN插值填充缺失值")
        print("=" * 40)
        
        # 按照utilts.py中的默认参数设置
        n_neighbors = 5  # KNN邻居数量
        weights = 'uniform'  # 权重计算方式
        
        print(f"KNN邻居数量: {n_neighbors}")
        print(f"权重计算方式: {weights}")
        print(f"插值前缺失值数量: {df_filtered.isnull().sum().sum()}")
        
        df_imputed = min_max_knn_impute(
            df_filtered,
            n_neighbors=n_neighbors,
            weights=weights
        )
        
        print(f"插值后缺失值数量: {df_imputed.isnull().sum().sum()}")
        
        # 4. 保存处理后的数据
        print("\n" + "=" * 40)
        print("步骤3: 保存处理后的数据")
        print("=" * 40)
        
        df_imputed.to_csv(output_file, index=False, encoding='utf-8')
        print(f"处理后的数据已保存到: {output_file}")
        print(f"最终数据形状: {df_imputed.shape}")
        
        # 5. 数据处理总结
        print("\n" + "=" * 60)
        print("数据处理总结")
        print("=" * 60)
        print(f"原始数据: {df.shape[0]} 行 × {df.shape[1]} 列")
        print(f"筛选后数据: {df_filtered.shape[0]} 行 × {df_filtered.shape[1]} 列")
        print(f"最终数据: {df_imputed.shape[0]} 行 × {df_imputed.shape[1]} 列")
        print(f"剔除变量数量: {len(df.columns) - len(df_filtered.columns)}")
        print(f"保留变量数量: {len(df_filtered.columns)}")
        print(f"原始缺失值数量: {df.isnull().sum().sum()}")
        print(f"最终缺失值数量: {df_imputed.isnull().sum().sum()}")
        
        # 显示保留的变量列表（前20个）
        print(f"\n保留的变量列表（前20个）:")
        for i, col in enumerate(df_imputed.columns[:20]):
            print(f"  {i+1:2d}. {col}")
        if len(df_imputed.columns) > 20:
            print(f"  ... 还有 {len(df_imputed.columns) - 20} 个变量")
        
        print("\n协变量数据处理完成！")
        
    except Exception as e:
        print(f"处理过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
